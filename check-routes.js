const fs = require('fs');
const path = require('path');

// All routes from navigation config
const allRoutes = [
  // Super Admin / Admin routes
  "/admin/dashboard",
  "/admin/students", 
  "/admin/teachers",
  "/admin/classes",
  "/admin/academic",
  "/admin/users",
  "/admin/finance",
  "/admin/analytics", 
  "/admin/settings",
  "/admin/attendance",
  "/admin/grades",
  "/admin/reports",

  // Teacher routes
  "/teacher/dashboard",
  "/teacher/classes",
  "/teacher/students", 
  "/teacher/attendance",
  "/teacher/grades",
  "/teacher/assignments",
  "/teacher/timetable",
  "/teacher/reports",

  // Student routes
  "/student/dashboard",
  "/student/grades",
  "/student/assignments",
  "/student/timetable", 
  "/student/attendance",
  "/student/library",
  "/student/exams",
  "/student/fees",

  // Parent routes
  "/parent/dashboard",
  "/parent/progress",
  "/parent/grades",
  "/parent/attendance", 
  "/parent/timetable",
  "/parent/fees",
  "/parent/messages",
  "/parent/events",

  // Admission Officer routes
  "/admission/dashboard",
  "/admission/applications",
  "/admission/register",
  "/admission/programs",
  "/admission/interviews", 
  "/admission/reports",

  // Finance Manager routes
  "/finance/dashboard",
  "/finance/fees",
  "/finance/payments",
  "/finance/invoices",
  "/finance/reports",
  "/finance/expenses",

  // Librarian routes
  "/library/dashboard",
  "/library/books",
  "/library/transactions",
  "/library/members",
  "/library/reservations",
  "/library/reports",

  // Transport Manager routes
  "/transport/dashboard", 
  "/transport/vehicles",
  "/transport/routes",
  "/transport/drivers",
  "/transport/maintenance",
  "/transport/students",
  "/transport/reports",

  // Hostel Manager routes
  "/hostel/dashboard",
  "/hostel/rooms",
  "/hostel/students", 
  "/hostel/allocations",
  "/hostel/maintenance",
  "/hostel/fees",
  "/hostel/reports"
];

function checkRouteExists(route) {
  // Convert route to file path
  const filePath = path.join(__dirname, 'app', route, 'page.tsx');
  return fs.existsSync(filePath);
}

console.log("=== ROUTE EXISTENCE CHECK ===\n");

const existingRoutes = [];
const missingRoutes = [];

allRoutes.forEach(route => {
  const exists = checkRouteExists(route);
  if (exists) {
    existingRoutes.push(route);
  } else {
    missingRoutes.push(route);
  }
  console.log(`${exists ? '✅' : '❌'} ${route}`);
});

console.log(`\n=== SUMMARY ===`);
console.log(`Total routes: ${allRoutes.length}`);
console.log(`Existing routes: ${existingRoutes.length}`);
console.log(`Missing routes: ${missingRoutes.length}`);

if (missingRoutes.length > 0) {
  console.log(`\n=== MISSING ROUTES ===`);
  missingRoutes.forEach(route => {
    console.log(`❌ ${route}`);
  });
}
