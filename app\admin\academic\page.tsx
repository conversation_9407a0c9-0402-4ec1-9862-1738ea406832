"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  School,
  GraduationCap,
  Users,
  BookOpen,
  BookMarked,
  Plus,
  Edit,
  Eye,
  Calendar,
  Clock,
  Target,
  TrendingUp,
  BarChart3,
  FileText,
  Settings,
} from "lucide-react";

export default function AdminAcademic() {
  const [user, setUser] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "super_admin" && parsedUser.role !== "admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock academic data
  const academicStats = {
    totalPrograms: 8,
    totalBatches: 24,
    totalClasses: 48,
    totalSubjects: 32,
    activeStudents: 1234,
    graduatingStudents: 156,
  };

  const programs = [
    {
      id: "PROG001",
      name: "Science Stream",
      duration: "2 Years",
      batches: 6,
      students: 480,
      subjects: ["Physics", "Chemistry", "Mathematics", "Biology", "English"],
      status: "Active",
      startDate: "2023-06-01",
      coordinator: "Dr. Emily Wilson",
    },
    {
      id: "PROG002", 
      name: "Commerce Stream",
      duration: "2 Years",
      batches: 4,
      students: 320,
      subjects: ["Accountancy", "Business Studies", "Economics", "Mathematics", "English"],
      status: "Active",
      startDate: "2023-06-01",
      coordinator: "Prof. Michael Chen",
    },
    {
      id: "PROG003",
      name: "Arts Stream",
      duration: "2 Years", 
      batches: 3,
      students: 240,
      subjects: ["History", "Geography", "Political Science", "English", "Psychology"],
      status: "Active",
      startDate: "2023-06-01",
      coordinator: "Ms. Sarah Johnson",
    },
    {
      id: "PROG004",
      name: "Computer Science",
      duration: "4 Years",
      batches: 8,
      students: 640,
      subjects: ["Programming", "Data Structures", "Algorithms", "Database", "Networks"],
      status: "Active",
      startDate: "2022-08-01",
      coordinator: "Dr. David Brown",
    },
  ];

  const recentBatches = [
    { id: "BATCH2024", name: "Science 2024-2026", program: "Science Stream", students: 80, status: "Active" },
    { id: "BATCH2023", name: "Commerce 2023-2025", program: "Commerce Stream", students: 75, status: "Active" },
    { id: "BATCH2022", name: "CS 2022-2026", program: "Computer Science", students: 120, status: "Active" },
    { id: "BATCH2021", name: "Arts 2021-2023", program: "Arts Stream", students: 60, status: "Graduating" },
  ];

  const subjectCategories = [
    { category: "Core Subjects", count: 12, color: "bg-blue-500" },
    { category: "Electives", count: 8, color: "bg-green-500" },
    { category: "Practical/Lab", count: 6, color: "bg-purple-500" },
    { category: "Extra-curricular", count: 6, color: "bg-orange-500" },
  ];

  const quickActions = [
    { title: "Add Program", icon: GraduationCap, color: "bg-blue-500", href: "/admin/programs" },
    { title: "Manage Batches", icon: Users, color: "bg-green-500", href: "/admin/batches" },
    { title: "Class Structure", icon: BookOpen, color: "bg-purple-500", href: "/admin/classes" },
    { title: "Subject Management", icon: BookMarked, color: "bg-orange-500", href: "/admin/subjects" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active": return "bg-green-100 text-green-700";
      case "Graduating": return "bg-blue-100 text-blue-700";
      case "Completed": return "bg-gray-100 text-gray-700";
      case "Suspended": return "bg-red-100 text-red-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Academic Management</h1>
            <p className="text-gray-600">Manage academic programs, batches, and curriculum structure</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Reports
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>

        {/* Academic Statistics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <GraduationCap className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {academicStats.totalPrograms}
                  </div>
                  <p className="text-sm text-gray-600">Academic Programs</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {academicStats.totalBatches}
                  </div>
                  <p className="text-sm text-gray-600">Active Batches</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {academicStats.totalClasses}
                  </div>
                  <p className="text-sm text-gray-600">Total Classes</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookMarked className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {academicStats.totalSubjects}
                  </div>
                  <p className="text-sm text-gray-600">Subjects</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {academicStats.activeStudents}
                  </div>
                  <p className="text-sm text-gray-600">Enrolled Students</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {academicStats.graduatingStudents}
                  </div>
                  <p className="text-sm text-gray-600">Graduating Soon</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  onClick={() => router.push(action.href)}
                >
                  <div className={`w-12 h-12 ${action.color} rounded-lg flex items-center justify-center`}>
                    <action.icon className="h-6 w-6 text-white" />
                  </div>
                  <span className="font-medium">{action.title}</span>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Academic Programs - Organized Table Layout */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <School className="h-5 w-5 mr-2" />
                Academic Programs
              </span>
              <Button onClick={() => router.push("/admin/programs/new")}>
                <Plus className="h-4 w-4 mr-2" />
                Add Program
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left p-4 font-medium text-gray-900">Program</th>
                    <th className="text-left p-4 font-medium text-gray-900">Duration</th>
                    <th className="text-left p-4 font-medium text-gray-900">Batches</th>
                    <th className="text-left p-4 font-medium text-gray-900">Students</th>
                    <th className="text-left p-4 font-medium text-gray-900">Subjects</th>
                    <th className="text-left p-4 font-medium text-gray-900">Coordinator</th>
                    <th className="text-left p-4 font-medium text-gray-900">Status</th>
                    <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {programs.map((program) => (
                    <tr key={program.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="p-4">
                        <div>
                          <div className="font-semibold text-gray-900">{program.name}</div>
                          <div className="text-sm text-gray-500">ID: {program.id}</div>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="text-sm text-gray-600">{program.duration}</span>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center">
                          <Users className="h-4 w-4 text-gray-400 mr-1" />
                          <span className="text-sm font-medium">{program.batches}</span>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center">
                          <Target className="h-4 w-4 text-gray-400 mr-1" />
                          <span className="text-sm font-medium">{program.students}</span>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center">
                          <BookMarked className="h-4 w-4 text-gray-400 mr-1" />
                          <span className="text-sm font-medium">{program.subjects.length}</span>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="text-sm text-gray-600">{program.coordinator}</span>
                      </td>
                      <td className="p-4">
                        <Badge className={getStatusColor(program.status)}>
                          {program.status}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Secondary Information Grid */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Recent Batches */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Recent Batches
                </span>
                <Button variant="outline" size="sm" onClick={() => router.push("/admin/batches")}>
                  View All
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentBatches.map((batch) => (
                  <div key={batch.id} className="p-4 border rounded-lg hover:shadow-sm transition-shadow">
                    <div className="flex items-center justify-between mb-2">
                      <div className="font-medium text-gray-900">{batch.name}</div>
                      <Badge className={getStatusColor(batch.status)}>
                        {batch.status}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-500 mb-2">{batch.program}</div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">
                        <Users className="h-4 w-4 inline mr-1" />
                        {batch.students} students
                      </span>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Subject Categories */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  Subject Distribution
                </span>
                <Button variant="outline" size="sm" onClick={() => router.push("/admin/subjects")}>
                  Manage
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {subjectCategories.map((category, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-4 h-4 ${category.color} rounded-full`} />
                      <span className="text-sm font-medium text-gray-700">{category.category}</span>
                    </div>
                    <span className="text-lg font-bold text-gray-900">{category.count}</span>
                  </div>
                ))}

                <div className="pt-4 border-t">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-3xl font-bold text-blue-600">{academicStats.totalSubjects}</div>
                    <div className="text-sm text-blue-600 font-medium">Total Subjects</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
